from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime
import logging

from app.database import database
from app.model import models, schemas
from app.model.dependency import get_current_user
from app.model.utils import HourliveException
from app.utils.wechat_crawler import wechat_crawler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

def update_tag_statistics(db: Session, tag_ids: List[int], read_increment: int = 0):
    """更新标签统计信息的后台任务"""
    try:
        for tag_id in tag_ids:
            tag = db.query(models.ArticleTag).filter(
                models.ArticleTag.id == tag_id
            ).first()
            
            if tag:
                # 重新计算文章数量
                article_count = db.query(models.Article).join(
                    models.Article.tags
                ).filter(
                    models.ArticleTag.id == tag_id
                ).count()
                
                tag.article_count = article_count
                
                # 如果有阅读增量，更新阅读统计
                if read_increment > 0:
                    tag.article_read_count += read_increment
                
        db.commit()
        
    except Exception as e:
        db.rollback()
        logger.error(f"更新标签统计失败: {str(e)}")

@router.post("/create", response_model=schemas.StandardResponse)
async def create_article(
    article: schemas.ArticleCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """创建文章"""
    try:
        # 检查作者是否存在
        author = db.query(models.ArticleAuthor).filter(
            models.ArticleAuthor.id == article.author_id
        ).first()
        
        if not author:
            raise HourliveException(message="文章作者不存在")
        
        # 检查标签是否存在
        tags = db.query(models.ArticleTag).filter(
            models.ArticleTag.id.in_(article.tag_ids)
        ).all()
        
        if len(tags) != len(article.tag_ids):
            raise HourliveException(message="部分标签不存在")
        
        # 创建文章
        article_data = article.dict(exclude={'tag_ids'})
        db_article = models.Article(
            **article_data,
            creator_id=current_user.id,
            updater_id=current_user.id
        )
        
        db.add(db_article)
        db.flush()  # 获取生成的ID
        
        # 关联标签
        db_article.tags = tags
        
        db.commit()
        db.refresh(db_article)
        
        # 后台更新标签统计
        background_tasks.add_task(update_tag_statistics, db, article.tag_ids)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleRead.from_orm(db_article),
            message="文章创建成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"创建文章失败: {str(e)}")
        raise HourliveException(message=f"创建失败: {str(e)}")

@router.get("/list", response_model=schemas.StandardResponse)
async def get_articles(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页条数"),
    title: Optional[str] = Query(None, description="标题模糊查询"),
    author_name: Optional[str] = Query(None, description="作者名模糊查询"),
    tag_id: Optional[int] = Query(None, description="标签精确查询"),
    channel: Optional[str] = Query(None, description="发布频道精确查询"),
    status: Optional[str] = Query(None, description="状态精确查询"),
    publish_start: Optional[datetime] = Query(None, description="发布时间开始"),
    publish_end: Optional[datetime] = Query(None, description="发布时间结束"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取文章列表"""
    try:
        query = db.query(models.Article).join(models.ArticleAuthor)
        
        # 添加筛选条件
        if title:
            query = query.filter(models.Article.title.contains(title))
        if author_name:
            query = query.filter(models.ArticleAuthor.author_name.contains(author_name))
        if tag_id:
            query = query.join(models.Article.tags).filter(models.ArticleTag.id == tag_id)
        if channel:
            query = query.filter(models.Article.channel == channel)
        if status:
            query = query.filter(models.Article.status == status)
        if publish_start:
            query = query.filter(models.Article.publish_time >= publish_start)
        if publish_end:
            query = query.filter(models.Article.publish_time <= publish_end)
        
        # 获取总数
        total_count = query.count()
        
        # 分页查询
        offset = (page - 1) * page_size
        articles = query.order_by(models.Article.publish_time.desc()).offset(offset).limit(page_size).all()
        
        return schemas.StandardResponse(
            success=True,
            data=[schemas.ArticleRead.from_orm(article) for article in articles],
            total_records=total_count,
            message="获取文章列表成功"
        )
        
    except Exception as e:
        logger.error(f"获取文章列表失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")
    

# 微信公众号爬取相关接口
@router.post("/crawl-wechat", response_model=schemas.StandardResponse)
async def crawl_wechat_article(
    request: schemas.WeChatCrawlRequest,
    current_user: models.User = Depends(get_current_user)
):
    """
    爬取微信公众号文章内容

    - **url**: 微信公众号文章URL（必填）

    返回爬取的完整HTML内容，包括：
    - 文章标题
    - 作者信息
    - 发布时间
    - 文章内容（完整的HTML页面内容，包含所有样式和结构）
    - 文章摘要
    - 封面图片
    """
    try:
        logger.info(f"用户 {current_user.account} 请求爬取微信文章: {request.url}")

        # 使用爬虫工具爬取文章
        raw_article_data = wechat_crawler.crawl_article(request.url)

        # 使用schemas模型验证和格式化数据
        article_data = schemas.WeChatArticleData(**raw_article_data)

        return schemas.StandardResponse(
            success=True,
            data=article_data.dict(),
            message="微信文章爬取成功"
        )

    except Exception as e:
        logger.error(f"微信文章爬取失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/validate-wechat-url", response_model=schemas.StandardResponse)
async def validate_wechat_url(
    url: str = Query(..., description="待验证的微信公众号文章URL"),
    current_user: models.User = Depends(get_current_user)
):
    """
    验证微信公众号文章URL是否有效

    - **url**: 待验证的URL

    返回验证结果和URL信息
    """
    try:
        is_valid = wechat_crawler.is_wechat_url(url)

        validation_result = schemas.WeChatUrlValidation(
            url=url,
            is_valid=is_valid,
            message="有效的微信公众号文章URL" if is_valid else "无效的微信公众号文章URL"
        )

        return schemas.StandardResponse(
            success=True,
            data=validation_result.dict(),
            message="URL验证完成"
        )

    except Exception as e:
        logger.error(f"URL验证失败: {str(e)}")
        raise HTTPException(status_code=400, detail=f"验证失败: {str(e)}")

@router.get("/{article_id}", response_model=schemas.StandardResponse)
async def get_article(
    article_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """获取单个文章详情"""
    try:
        article = db.query(models.Article).filter(
            models.Article.id == article_id
        ).first()
        
        if not article:
            raise HourliveException(message="文章不存在")
        
        # 增加浏览数
        article.view_count += 1
        db.commit()
        
        # 后台更新标签阅读统计
        tag_ids = [tag.id for tag in article.tags]
        background_tasks.add_task(update_tag_statistics, db, tag_ids, 1)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleRead.from_orm(article),
            message="获取文章详情成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        logger.error(f"获取文章详情失败: {str(e)}")
        raise HourliveException(message=f"获取失败: {str(e)}")

@router.put("/{article_id}", response_model=schemas.StandardResponse)
async def update_article(
    article_id: int,
    article_update: schemas.ArticleUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """更新文章"""
    try:
        article = db.query(models.Article).filter(
            models.Article.id == article_id
        ).first()
        
        if not article:
            raise HourliveException(message="文章不存在")
        
        # 检查作者是否存在
        if article_update.author_id and article_update.author_id != article.author_id:
            author = db.query(models.ArticleAuthor).filter(
                models.ArticleAuthor.id == article_update.author_id
            ).first()
            if not author:
                raise HourliveException(message="文章作者不存在")
        
        # 处理标签更新
        old_tag_ids = [tag.id for tag in article.tags]
        new_tag_ids = article_update.tag_ids if article_update.tag_ids is not None else old_tag_ids
        
        if article_update.tag_ids is not None:
            # 检查标签是否存在
            tags = db.query(models.ArticleTag).filter(
                models.ArticleTag.id.in_(article_update.tag_ids)
            ).all()
            
            if len(tags) != len(article_update.tag_ids):
                raise HourliveException(message="部分标签不存在")
            
            # 更新标签关联
            article.tags = tags
        
        # 更新其他字段
        update_data = article_update.dict(exclude_unset=True, exclude={'tag_ids'})
        for field, value in update_data.items():
            setattr(article, field, value)
        
        article.updater_id = current_user.id
        
        db.commit()
        db.refresh(article)
        
        # 后台更新标签统计（包括旧标签和新标签）
        all_tag_ids = list(set(old_tag_ids + new_tag_ids))
        background_tasks.add_task(update_tag_statistics, db, all_tag_ids)
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleRead.from_orm(article),
            message="文章更新成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新文章失败: {str(e)}")
        raise HourliveException(message=f"更新失败: {str(e)}")

@router.delete("/{article_id}", response_model=schemas.StandardResponse)
async def delete_article(
    article_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """删除文章"""
    try:
        article = db.query(models.Article).filter(
            models.Article.id == article_id
        ).first()
        
        if not article:
            raise HourliveException(message="文章不存在")
        
        # 获取关联的标签ID用于更新统计
        tag_ids = [tag.id for tag in article.tags]
        
        db.delete(article)
        db.commit()
        
        # 后台更新标签统计
        background_tasks.add_task(update_tag_statistics, db, tag_ids)
        
        return schemas.StandardResponse(
            success=True,
            message="文章删除成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除文章失败: {str(e)}")
        raise HourliveException(message=f"删除失败: {str(e)}")

@router.post("/{article_id}/like", response_model=schemas.StandardResponse)
async def like_article(
    article_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """点赞文章"""
    try:
        article = db.query(models.Article).filter(
            models.Article.id == article_id
        ).first()
        
        if not article:
            raise HourliveException(message="文章不存在")
        
        article.like_count += 1
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            data={"like_count": article.like_count},
            message="点赞成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"点赞文章失败: {str(e)}")
        raise HourliveException(message=f"点赞失败: {str(e)}")

@router.post("/{article_id}/favorite", response_model=schemas.StandardResponse)
async def favorite_article(
    article_id: int,
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """收藏文章"""
    try:
        article = db.query(models.Article).filter(
            models.Article.id == article_id
        ).first()
        
        if not article:
            raise HourliveException(message="文章不存在")
        
        article.favorite_count += 1
        db.commit()
        
        return schemas.StandardResponse(
            success=True,
            data={"favorite_count": article.favorite_count},
            message="收藏成功"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"收藏文章失败: {str(e)}")
        raise HourliveException(message=f"收藏失败: {str(e)}")

@router.put("/{article_id}/status", response_model=schemas.StandardResponse)
async def update_article_status(
    article_id: int,
    background_tasks: BackgroundTasks,
    status: str = Query(..., regex="^[0-3]$", description="状态：0.草稿 1.待审核 2.审核中 3.已发布"),
    db: Session = Depends(database.get_db),
    current_user: models.User = Depends(get_current_user)
):
    """更新文章状态"""
    try:
        article = db.query(models.Article).filter(
            models.Article.id == article_id
        ).first()
        
        if not article:
            raise HourliveException(message="文章不存在")
        
        old_status = article.status
        article.status = status
        article.updater_id = current_user.id
        
        db.commit()
        
        # 如果状态变为已发布或从已发布变为其他状态，更新标签统计
        if old_status != "3" and status == "3" or old_status == "3" and status != "3":
            tag_ids = [tag.id for tag in article.tags]
            background_tasks.add_task(update_tag_statistics, db, tag_ids)
        
        status_map = {"0": "草稿", "1": "待审核", "2": "审核中", "3": "已发布"}
        
        return schemas.StandardResponse(
            success=True,
            data=schemas.ArticleRead.from_orm(article),
            message=f"文章状态已更新为：{status_map.get(status, status)}"
        )
        
    except HourliveException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新文章状态失败: {str(e)}")
        raise HourliveException(message=f"更新失败: {str(e)}")