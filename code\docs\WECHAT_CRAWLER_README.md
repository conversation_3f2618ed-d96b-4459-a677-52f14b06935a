# 微信公众号文章爬取功能

## 功能概述

微信公众号文章爬取功能可以从微信公众号URL获取完整的HTML页面内容，直接将整个HTML标签的内容作为文章的富文本内容返回给前端。

## 核心特性

- ✅ **完整HTML内容**: 直接返回整个HTML页面内容，保留所有原始样式和结构
- ✅ **安全处理**: 自动移除script、iframe等可能有安全风险的标签
- ✅ **CSS链接修复**: 自动修复缺少https协议的CSS样式文件链接
- ✅ **图片标签优化**: 确保所有图片都有正确的src属性和alt属性
- ✅ **隐藏内容恢复**: 自动恢复被隐藏的div标签，确保内容可见
- ✅ **HTML内容压缩**: 移除多余的换行和空白，优化传输效率
- ✅ **富文本兼容**: 返回的HTML内容可直接用于富文本编辑器显示

## API接口

### 1. 爬取微信文章

**接口**: `POST /article/crawl-wechat`

**请求参数**:
```json
{
    "url": "https://mp.weixin.qq.com/s/xxxxxx"
}
```

**返回数据**:
```json
{
    "success": true,
    "data": {
        "title": "文章标题",
        "author": "作者信息",
        "publish_time": "发布时间",
        "content": "完整的HTML页面内容（包含所有样式和结构）",
        "summary": "文章摘要",
        "cover_image": "封面图片URL",
        "original_url": "原始文章URL",
        "crawl_time": "爬取时间"
    },
    "message": "微信文章爬取成功"
}
```

### 2. 验证微信URL

**接口**: `GET /article/validate-wechat-url`

**请求参数**: `url` (查询参数)

**返回数据**:
```json
{
    "success": true,
    "data": {
        "url": "验证的URL",
        "is_valid": true,
        "message": "验证结果说明"
    },
    "message": "URL验证完成"
}
```

## 使用示例

### JavaScript集成

```javascript
// 导入微信文章到富文本编辑器
async function importWeChatArticle(url) {
    try {
        const response = await fetch('/article/crawl-wechat', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer YOUR_TOKEN',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ url })
        })
        
        const result = await response.json()
        
        if (result.success) {
            // 直接设置HTML内容，样式会完美保留
            editor.setHtml(result.data.content)
            
            // 设置其他字段
            document.getElementById('title').value = result.data.title
            document.getElementById('author').value = result.data.author
            document.getElementById('summary').value = result.data.summary
            
            console.log('✅ 微信文章导入成功，样式完美保留')
        }
    } catch (error) {
        console.error('导入失败:', error)
    }
}
```

### Python调用

```python
import requests

def crawl_wechat_article(url, token):
    """爬取微信公众号文章"""
    response = requests.post(
        'http://localhost:9000/article/crawl-wechat',
        headers={
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        },
        json={'url': url}
    )
    
    if response.status_code == 200:
        result = response.json()
        if result['success']:
            return result['data']
    
    return None

# 使用示例
article_data = crawl_wechat_article(
    'https://mp.weixin.qq.com/s/example',
    'your_token'
)

if article_data:
    print(f"标题: {article_data['title']}")
    print(f"内容长度: {len(article_data['content'])}")
```

## 完整HTML内容返回技术

### 问题解决
原问题：微信公众号内容填充到富文本编辑器后只显示文字，CSS样式无法正确展示。

### 解决方案
直接返回完整的HTML页面内容，保留所有原始样式和结构：

**返回内容**:
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>文章标题</title>
    <style>
        .content p { color: #333; font-size: 16px; }
        .rich_media_title { font-weight: bold; }
    </style>
</head>
<body>
    <div class="content">
        <h1 class="rich_media_title">文章标题</h1>
        <p>完整保留所有样式效果</p>
        <img src="image.jpg" alt="图片">
    </div>
</body>
</html>
```

### 技术特点
- **完整HTML结构**: 保留DOCTYPE、html、head、body等完整结构
- **样式完整保留**: 保留所有CSS样式表和内联样式
- **安全处理**: 自动移除script、iframe等可能有安全风险的标签
- **兼容性优化**: 确保所有富文本编辑器都能正确显示

## 支持的富文本编辑器

- ✅ wangEditor
- ✅ TinyMCE
- ✅ Quill.js
- ✅ CKEditor
- ✅ UEditor
- ✅ 其他所有主流富文本编辑器

## 注意事项

### 1. 认证要求
- 所有API接口都需要有效的用户认证token
- 确保用户有相应的访问权限

### 2. URL格式
- 仅支持微信公众号文章URL
- URL格式：`https://mp.weixin.qq.com/s/...`

### 3. 网络要求
- 需要稳定的网络连接访问微信服务器
- 爬取过程可能需要几秒钟时间

### 4. 内容限制
- 部分文章可能需要在微信客户端中打开
- 某些图片可能存在防盗链限制

## 错误处理

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| 无效的微信公众号文章URL | URL格式不正确 | 检查URL格式 |
| 请求超时 | 网络连接问题 | 检查网络，重试 |
| 文章需要在微信客户端中打开 | 文章访问限制 | 尝试其他文章 |
| 认证失败 | Token无效 | 更新认证token |

### 错误处理示例

```javascript
async function safeImportWeChatArticle(url) {
    try {
        const result = await importWeChatArticle(url)
        return result
    } catch (error) {
        console.error('导入失败:', error)
        
        if (error.message.includes('URL')) {
            alert('请输入有效的微信公众号文章链接')
        } else if (error.message.includes('超时')) {
            alert('网络请求超时，请重试')
        } else if (error.message.includes('认证')) {
            alert('认证失败，请重新登录')
        } else {
            alert('导入失败，请稍后重试')
        }
        
        return null
    }
}
```

## 技术实现

### 核心文件
- `app/utils/wechat_crawler.py` - 微信爬取工具类
- `app/model/schemas.py` - 数据模型定义
- `app/routers/biz_routes/article_api_routers.py` - API路由

### 主要方法
- `crawl_article()` - 爬取文章主方法
- `extract_article_content()` - 提取完整HTML内容
- `_remove_unsafe_elements()` - 移除不安全的HTML元素
- `_fix_css_links()` - 修复CSS样式文件链接
- `_fix_image_tags()` - 修复图片标签和属性
- `_restore_hidden_divs()` - 恢复被隐藏的div标签
- `_compress_html()` - 压缩HTML内容
- `is_wechat_url()` - URL格式验证
- `_extract_cover_image()` - 封面图片提取

## 更新日志

### v3.1 (当前版本)
- ✅ 新增CSS链接修复功能，自动补全https协议
- ✅ 新增图片标签优化，确保src属性和alt属性完整
- ✅ 新增隐藏内容恢复功能，自动显示被隐藏的div标签
- ✅ 新增HTML内容压缩功能，优化传输效率
- ✅ 增强图片处理逻辑，支持data-src和src双重处理

### v3.0
- ✅ 直接返回完整HTML页面内容，保留所有原始样式和结构
- ✅ 增强安全处理，移除script、iframe等可能有安全风险的标签
- ✅ 保留完整的CSS样式表和内联样式
- ✅ 优化HTML结构，确保富文本编辑器完美显示

### v2.0
- ✅ 新增CSS内联化功能，解决富文本编辑器样式显示问题
- ✅ 优化图片处理，支持响应式显示
- ✅ 增强错误处理和异常捕获
- ✅ 提升爬取成功率和内容质量

### v1.0
- ✅ 基础微信文章爬取功能
- ✅ 支持标题、作者、内容、摘要提取
- ✅ 基础的HTML清理和优化

## 总结

微信公众号文章爬取功能现在直接返回完整的HTML页面内容，完美保留所有原始样式和结构。通过安全处理确保内容安全，同时保证富文本编辑器能够完美显示所有样式效果。功能稳定可靠，适用于各种内容管理和编辑场景。
