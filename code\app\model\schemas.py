from pydantic import BaseModel
from datetime import datetime, date
from typing import Optional, Any, Union
from pydantic_sqlalchemy import sqlalchemy_to_pydantic
from app.model.models import User, Role, Auth
from pydantic import BaseModel, Field, validator
from typing import List

class Token(BaseModel):
    access_token: str
    token_type: str


class StandardResponse(BaseModel):
    success: bool
    data: Optional[Any] = None
    message: Optional[str] = None
    token: Optional[Token] = None
    status_code: int = 200
    toast: Optional[str] = None
    total_records: int = 0  # 新增分页总记录数，默认值为0


UserBase = sqlalchemy_to_pydantic(User)


class UserRead(UserBase):
    hashed_password: Optional[str] = None


RoleRead = sqlalchemy_to_pydantic(Role)

AuthRead = sqlalchemy_to_pydantic(Auth)


class WechatUserBase(BaseModel):
    openid: str
    nickname: Optional[str] = None
    headimgurl: Optional[str] = None
    subscribe: bool = False
    
class WechatUserCreate(WechatUserBase):
    pass
    
class WechatUser(WechatUserBase):
    id: int
    user_id: Optional[int] = None
    subscribe_time: Optional[datetime] = None
    
    class Config:
        orm_mode = True
        
class QRCodeTicketBase(BaseModel):
    scene_str: str
    
class QRCodeTicketCreate(QRCodeTicketBase):
    ticket: str
    url: str
    expire_time: datetime
    
class QRCodeTicket(QRCodeTicketBase):
    id: int
    ticket: str
    url: str
    expire_time: datetime
    is_used: bool
    created_at: datetime
    
    class Config:
        orm_mode = True
        
class WechatLoginResponse(BaseModel):
    qrcode_url: str
    scene_str: str
    expire_time: datetime


class Brand(BaseModel):
    name: str

# 先定义LinkGroup相关的schema
class LinkGroupBase(BaseModel):
    name: str = Field(..., description="连接名称")
    url: str = Field(..., description="连接地址")

class LinkGroupCreate(LinkGroupBase):
    site_config_id: int = Field(..., description="关联的网站配置ID")

class LinkGroupUpdate(BaseModel):
    name: Optional[str] = Field(None, description="连接名称")
    url: Optional[str] = Field(None, description="连接地址")

class LinkGroupRead(LinkGroupBase):
    id: int
    create_time: datetime
    update_time: datetime
    site_config_id: int
    creator_id: int
    updater_id: int
    
    class Config:
        orm_mode = True

# 然后定义SiteConfig相关的schema
class SiteConfigBase(BaseModel):
    title: str = Field(..., max_length=255, description="网站标题")
    link_url: str = Field(..., max_length=255, description="标题链接URL")
    prompt_text: str = Field(..., max_length=500, description="提示内容文本")
    logo: Optional[str] = Field(None, max_length=255, description="logo图片路径")
    is_enabled: bool = Field(default=True, description="是否启用")
    is_fixed_position: bool = Field(default=False, description="是否固定位置")
    is_new_page: bool = Field(default=False, description="是否跳转新页面")
    publish_time: Optional[Union[datetime, str]] = Field(None, description="上架时间")
    offline_time: Optional[Union[datetime, str]] = Field(None, description="下架时间")
    partner_code: str = Field(..., max_length=100, description="合作伙伴代码")
    is_hover_enabled: bool = Field(default=False, description="hover是否启用")
    
    @validator('publish_time', 'offline_time', pre=True)
    def parse_datetime(cls, v):
        if isinstance(v, str):
            if v.strip() == "":
                return None
            # 如果是字符串，尝试解析为datetime
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                raise ValueError(f"无效的时间格式: {v}")
        return v

class SiteConfigCreate(BaseModel):
    title: str = Field(..., description="网站标题")
    link_url: str = Field(..., description="标题链接URL")
    prompt_text: str = Field(..., description="提示内容文本")
    logo: Optional[str] = Field(None, description="logo图片路径")
    is_enabled: bool = Field(True, description="是否启用，True启用，False禁用")
    is_fixed_position: bool = Field(False, description="是否固定位置，True是，False否")
    is_new_page: bool = Field(False, description="是否跳转新页面，True是，False否")
    publish_time: Optional[datetime] = Field(None, description="上架时间")
    offline_time: Optional[datetime] = Field(None, description="下架时间")
    partner_code: str = Field(..., description="合作伙伴代码")
    is_hover_enabled: bool = Field(False, description="hover是否启用，True启用，False禁用")
    link_groups: Optional[List[LinkGroupBase]] = Field([], description="连接组列表")

class SiteConfigUpdate(BaseModel):
    title: Optional[str] = Field(None, description="网站标题")
    link_url: Optional[str] = Field(None, description="标题链接URL")
    prompt_text: Optional[str] = Field(None, description="提示内容文本")
    logo: Optional[str] = Field(None, description="logo图片路径")
    is_enabled: Optional[bool] = Field(None, description="是否启用，True启用，False禁用")
    is_fixed_position: Optional[bool] = Field(None, description="是否固定位置，True是，False否")
    is_new_page: Optional[bool] = Field(None, description="是否跳转新页面，True是，False否")
    publish_time: Optional[datetime] = Field(None, description="上架时间")
    offline_time: Optional[datetime] = Field(None, description="下架时间")
    partner_code: Optional[str] = Field(None, description="合作伙伴代码")
    is_hover_enabled: Optional[bool] = Field(None, description="hover是否启用，True启用，False禁用")
    link_groups: Optional[List[LinkGroupBase]] = Field(None, description="连接组列表")

class SiteConfigRead(BaseModel):
    id: int
    title: str
    link_url: str
    prompt_text: str
    logo: Optional[str]  # 修改为可选字段
    is_enabled: bool
    is_fixed_position: bool
    is_new_page: bool
    publish_time: Optional[datetime]
    offline_time: Optional[datetime]
    partner_code: str
    is_hover_enabled: bool
    create_time: datetime
    update_time: datetime
    creator_id: int
    updater_id: int
    
    class Config:
        orm_mode = True

# 标签相关的schema定义
class TagBase(BaseModel):
    name: str = Field(..., max_length=50, description="标签名称")
    description: Optional[str] = Field(None, max_length=255, description="标签描述")

class TagCreate(TagBase):
    pass

class TagUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=50, description="标签名称")
    description: Optional[str] = Field(None, max_length=255, description="标签描述")

class TagRead(TagBase):
    id: int
    create_time: datetime
    update_time: datetime
    creator_id: int
    
    class Config:
        orm_mode = True

# 图片相关的schema定义
class ImageBase(BaseModel):
    original_name: str = Field(..., max_length=255, description="图片原始名称")
    description: Optional[str] = Field(None, description="图片描述")
    is_public: bool = Field(default=True, description="是否公开访问")
    category: Optional[str] = Field(None, max_length=50, description="图片分类")

class ImageCreate(ImageBase):
    tag_ids: Optional[List[int]] = Field(default=[], description="关联的标签ID列表")

class ImageUpdate(BaseModel):
    original_name: Optional[str] = Field(None, max_length=255, description="图片原始名称")
    description: Optional[str] = Field(None, description="图片描述")
    is_public: Optional[bool] = Field(None, description="是否公开访问")
    category: Optional[str] = Field(None, max_length=50, description="图片分类")
    tag_ids: Optional[List[int]] = Field(None, description="关联的标签ID列表")

class ImageRead(ImageBase):
    id: int
    create_time: datetime
    update_time: datetime
    new_name: str
    access_url: str
    file_size: Optional[int]
    file_type: Optional[str]
    width: Optional[int]
    height: Optional[int]
    uploader_id: int
    tags: List[TagRead] = []
    
    class Config:
        orm_mode = True

# 图片上传响应
class ImageUploadResponse(BaseModel):
    image_info: ImageRead
    upload_status: str

# 图片搜索请求
class ImageSearchRequest(BaseModel):
    tag_ids: Optional[List[int]] = Field(None, description="按标签筛选")
    category: Optional[str] = Field(None, description="按分类筛选")
    keyword: Optional[str] = Field(None, description="关键词搜索（名称或描述）")
    is_public: Optional[bool] = Field(None, description="是否公开")
    uploader_id: Optional[int] = Field(None, description="上传者ID")
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")

# 更新SiteConfigRead以包含link_groups
class SiteConfigReadWithLinks(SiteConfigRead):
    link_groups: List[LinkGroupRead] = []
    
    class Config:
        orm_mode = True

# 标签分类相关 Schema
class ArticleCategoryBase(BaseModel):
    name: str = Field(..., max_length=20, description="分类名称")
    weight: str = Field(..., regex="^[1-5]$", description="分类权重：1-5")
    seo_title: Optional[str] = Field(None, max_length=100, description="SEO标题")
    seo_keywords: Optional[str] = Field(None, max_length=100, description="SEO关键字")
    seo_description: Optional[str] = Field(None, max_length=300, description="SEO描述")

class ArticleCategoryCreate(ArticleCategoryBase):
    pass

class ArticleCategoryUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=20, description="分类名称")
    weight: Optional[str] = Field(None, regex="^[1-5]$", description="分类权重：1-5")
    seo_title: Optional[str] = Field(None, max_length=100, description="SEO标题")
    seo_keywords: Optional[str] = Field(None, max_length=100, description="SEO关键字")
    seo_description: Optional[str] = Field(None, max_length=300, description="SEO描述")

class ArticleCategoryRead(ArticleCategoryBase):
    id: int
    create_time: datetime
    update_time: datetime
    creator_id: int
    updater_id: int
    
    class Config:
        orm_mode = True

# 文章作者相关 Schema
class ArticleAuthorBase(BaseModel):
    wechat_nickname: str = Field(..., max_length=30, description="微信昵称")
    author_name: str = Field(..., max_length=30, description="作者名")

class ArticleAuthorCreate(ArticleAuthorBase):
    pass

class ArticleAuthorUpdate(BaseModel):
    wechat_nickname: Optional[str] = Field(None, max_length=30, description="微信昵称")
    author_name: Optional[str] = Field(None, max_length=30, description="作者名")

class ArticleAuthorRead(ArticleAuthorBase):
    id: int
    create_time: datetime
    update_time: datetime
    creator_id: int
    updater_id: int
    
    class Config:
        orm_mode = True

# 文章标签相关 Schema
class ArticleTagBase(BaseModel):
    name: str = Field(..., max_length=20, description="标签名")
    category_id: int = Field(..., description="标签分类ID")

class ArticleTagCreate(ArticleTagBase):
    pass

class ArticleTagUpdate(BaseModel):
    name: Optional[str] = Field(None, max_length=20, description="标签名")
    category_id: Optional[int] = Field(None, description="标签分类ID")

class ArticleTagRead(ArticleTagBase):
    id: int
    first_letter: str
    article_count: int
    article_read_count: int
    create_time: datetime
    update_time: datetime
    creator_id: int
    updater_id: int
    category: ArticleCategoryRead
    
    class Config:
        orm_mode = True

# 文章相关 Schema
class ArticleBase(BaseModel):
    title: str = Field(..., max_length=100, description="标题")
    style: str = Field(..., regex="^(富文本|markdown)$", description="风格：富文本、markdown")
    content: str = Field(..., description="文章内容")
    channel: str = Field(..., regex="^(头条|百科|快讯)$", description="发布频道：头条、百科、快讯")
    cover_image: Optional[str] = Field(None, max_length=500, description="文章封面")
    summary: Optional[str] = Field(None, max_length=300, description="简介")
    share_image: Optional[str] = Field(None, max_length=500, description="分享封面")
    publish_time: datetime = Field(..., description="发表时间")
    is_visible: bool = Field(True, description="是否展示")
    seo_title: Optional[str] = Field(None, max_length=100, description="SEO标题")
    seo_keywords: Optional[str] = Field(None, max_length=100, description="SEO关键字")
    seo_description: Optional[str] = Field(None, max_length=300, description="SEO描述")
    status: str = Field("0", regex="^[0-3]$", description="状态：0.草稿 1.待审核 2.审核中 3.已发布")
    author_id: int = Field(..., description="作者ID")

class ArticleCreate(ArticleBase):
    tag_ids: List[int] = Field(..., description="标签ID列表")

class ArticleUpdate(BaseModel):
    title: Optional[str] = Field(None, max_length=100, description="标题")
    style: Optional[str] = Field(None, regex="^(富文本|markdown)$", description="风格")
    content: Optional[str] = Field(None, description="文章内容")
    channel: Optional[str] = Field(None, regex="^(头条|百科|快讯)$", description="发布频道")
    cover_image: Optional[str] = Field(None, max_length=500, description="文章封面")
    summary: Optional[str] = Field(None, max_length=300, description="简介")
    share_image: Optional[str] = Field(None, max_length=500, description="分享封面")
    publish_time: Optional[datetime] = Field(None, description="发表时间")
    is_visible: Optional[bool] = Field(None, description="是否展示")
    seo_title: Optional[str] = Field(None, max_length=100, description="SEO标题")
    seo_keywords: Optional[str] = Field(None, max_length=100, description="SEO关键字")
    seo_description: Optional[str] = Field(None, max_length=300, description="SEO描述")
    status: Optional[str] = Field(None, regex="^[0-3]$", description="状态")
    author_id: Optional[int] = Field(None, description="作者ID")
    tag_ids: Optional[List[int]] = Field(None, description="标签ID列表")

class ArticleRead(ArticleBase):
    id: int
    view_count: int
    like_count: int
    favorite_count: int
    comment_count: int
    create_time: datetime
    update_time: datetime
    creator_id: int
    updater_id: int
    author: ArticleAuthorRead
    tags: List[ArticleTagRead] = []

    class Config:
        orm_mode = True

# 用户端文章查询相关 Schema
class PublicArticleAuthor(BaseModel):
    """用户端文章作者信息（简化版）"""
    id: int
    wechat_nickname: str = Field(..., description="微信昵称")
    author_name: str = Field(..., description="作者名")

    class Config:
        orm_mode = True

class PublicArticleTag(BaseModel):
    """用户端文章标签信息（简化版）"""
    id: int
    name: str = Field(..., description="标签名")

    class Config:
        orm_mode = True

class PublicArticleListItem(BaseModel):
    """用户端文章列表项"""
    id: int
    title: str = Field(..., description="文章标题")
    cover_image: Optional[str] = Field(None, description="封面图")
    summary: Optional[str] = Field(None, description="文章简介")
    publish_time: datetime = Field(..., description="发表时间")
    author: PublicArticleAuthor = Field(..., description="作者信息")
    tags: List[PublicArticleTag] = Field(default=[], description="标签列表")

    class Config:
        orm_mode = True

class PublicArticleDetail(BaseModel):
    """用户端文章详情"""
    id: int
    title: str = Field(..., description="文章标题")
    cover_image: Optional[str] = Field(None, description="封面图")
    summary: Optional[str] = Field(None, description="文章简介")
    content: str = Field(..., description="文章内容")
    style: str = Field(..., description="文章风格")
    publish_time: datetime = Field(..., description="发表时间")
    view_count: int = Field(..., description="浏览量")
    like_count: int = Field(default=0, description="点赞数")
    favorite_count: int = Field(default=0, description="收藏数")
    comment_count: int = Field(default=0, description="评论数")
    author: PublicArticleAuthor = Field(..., description="作者信息")
    tags: List[PublicArticleTag] = Field(default=[], description="标签列表")

    class Config:
        orm_mode = True

# 微信公众号爬取相关 Schema
class WeChatCrawlRequest(BaseModel):
    """微信公众号爬取请求模型"""
    url: str = Field(..., description="微信公众号文章URL", example="https://mp.weixin.qq.com/s/xxxxxx")

    class Config:
        schema_extra = {
            "example": {
                "url": "https://mp.weixin.qq.com/s/abc123def456"
            }
        }

class WeChatArticleData(BaseModel):
    """微信公众号文章数据模型"""
    title: str = Field(..., description="文章标题")
    author: str = Field(..., description="作者信息")
    publish_time: str = Field(..., description="发布时间")
    content: str = Field(..., description="文章内容（完整的HTML页面内容，包含所有样式和结构）")
    summary: str = Field(..., description="文章摘要")
    cover_image: str = Field(..., description="封面图片URL")
    original_url: str = Field(..., description="原始文章URL")
    crawl_time: str = Field(..., description="爬取时间")

    class Config:
        schema_extra = {
            "example": {
                "title": "2024年人工智能发展趋势分析",
                "author": "科技前沿",
                "publish_time": "2024-01-15",
                "content": "<!DOCTYPE html><html><head><meta charset=\"utf-8\"><title>2024年人工智能发展趋势分析</title><style>body{font-family:Arial,sans-serif;line-height:1.6;}</style></head><body><div class=\"rich_media_content\"><p>随着人工智能技术的快速发展...</p><img src=\"https://mmbiz.qpic.cn/image.jpg\" alt=\"AI发展图\"/><h2>技术突破</h2><p>在机器学习领域...</p></div></body></html>",
                "summary": "随着人工智能技术的快速发展，2024年将是AI应用爆发的关键一年...",
                "cover_image": "https://mmbiz.qpic.cn/mmbiz_jpg/cover123.jpg",
                "original_url": "https://mp.weixin.qq.com/s/abc123def456",
                "crawl_time": "2024-01-15T10:30:00"
            }
        }

class WeChatUrlValidation(BaseModel):
    """微信公众号URL验证结果模型"""
    url: str = Field(..., description="验证的URL")
    is_valid: bool = Field(..., description="是否为有效的微信公众号URL")
    message: str = Field(..., description="验证结果说明")

    class Config:
        schema_extra = {
            "example": {
                "url": "https://mp.weixin.qq.com/s/abc123",
                "is_valid": True,
                "message": "有效的微信公众号文章URL"
            }
        }

