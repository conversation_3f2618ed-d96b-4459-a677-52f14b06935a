#!/usr/bin/env python3
"""
微信公众号文章爬取工具类

支持从微信公众号URL爬取富文本内容，包括标题、作者、内容、图片等信息
"""

import re
import json
import logging
import requests
from typing import Dict, Optional, List
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup
from datetime import datetime
# CSS内联化处理不需要外部库

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WeChatCrawler:
    """微信公众号文章爬取器"""
    
    def __init__(self):
        self.session = requests.Session()
        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
    def is_wechat_url(self, url: str) -> bool:
        """
        验证是否为有效的微信公众号文章URL
        
        Args:
            url: 待验证的URL
            
        Returns:
            bool: 是否为有效的微信公众号URL
        """
        try:
            parsed_url = urlparse(url)
            # 检查域名是否为微信公众号域名
            valid_domains = ['mp.weixin.qq.com']
            if parsed_url.netloc not in valid_domains:
                return False
            
            # 检查路径是否包含文章标识
            if '/s/' not in parsed_url.path and '/s?' not in parsed_url.query:
                return False
                
            return True
        except Exception as e:
            logger.error(f"URL验证失败: {str(e)}")
            return False
    
    def extract_article_content(self, html: str) -> Dict:
        """
        从HTML中提取文章内容，直接返回完整的HTML页面内容作为富文本

        Args:
            html: 网页HTML内容

        Returns:
            Dict: 提取的文章信息
        """
        try:
            soup = BeautifulSoup(html, 'html.parser')

            # 提取文章标题
            title_elem = soup.find('h1', {'class': 'rich_media_title'}) or soup.find('h1')
            title = title_elem.get_text(strip=True) if title_elem else ""

            # 提取作者信息
            author_elem = soup.find('a', {'class': 'rich_media_meta_link'}) or soup.find('span', {'class': 'rich_media_meta_text'})
            author = author_elem.get_text(strip=True) if author_elem else ""

            # 提取发布时间
            time_elem = soup.find('em', {'class': 'rich_media_meta_text'})
            publish_time = time_elem.get_text(strip=True) if time_elem else ""

            # 🔥 直接使用整个HTML页面内容作为富文本内容
            # 移除可能有安全风险的标签
            self._remove_unsafe_elements(soup)

            # 处理CSS样式文件链接
            self._fix_css_links(soup)

            # 处理图片标签
            self._fix_image_tags(soup)

            # 恢复隐藏的div标签
            self._restore_hidden_divs(soup)

            # 优化HTML结构，确保浏览器正常显示
            self._optimize_for_browser_display(soup)

            # 获取完整的HTML内容
            content_html = str(soup)

            # 压缩HTML内容
            content_html = self._compress_html(content_html)

            # 提取摘要（从整个页面文本中提取前200个字符）
            content_text = soup.get_text()
            summary = content_text[:200] + "..." if len(content_text) > 200 else content_text

            # 提取封面图
            cover_image = self._extract_cover_image(soup)

            return {
                'title': title or "未知标题",
                'author': author or "未知作者",
                'publish_time': publish_time or datetime.now().strftime("%Y-%m-%d"),
                'content': content_html or "",
                'summary': summary or "",
                'cover_image': cover_image or "",
                'original_url': '',  # 将在调用处设置
                'crawl_time': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"内容提取失败: {str(e)}")
            raise Exception(f"内容提取失败: {str(e)}")

    def _remove_unsafe_elements(self, soup):
        """
        移除可能有安全风险的HTML元素和微信特有的检测代码

        Args:
            soup: BeautifulSoup对象
        """
        # 移除所有script标签以避免XSS攻击和微信环境检测
        for script in soup.find_all('script'):
            script.decompose()

        # 移除noscript标签（通常包含微信提示信息）
        for noscript in soup.find_all('noscript'):
            noscript.decompose()

        # 移除style标签中的JavaScript和微信相关样式
        for style in soup.find_all('style'):
            if style.string:
                css_content = style.string
                # 移除包含JavaScript的样式
                if 'javascript:' in css_content.lower() or 'expression(' in css_content.lower():
                    style.decompose()
                    continue

                # 移除微信遮罩相关的CSS样式
                wechat_css_patterns = [
                    r'\.weui-mask\s*{[^}]*}',
                    r'\.weui-dialog\s*{[^}]*}',
                    r'\.wx_dialog[^{]*{[^}]*}',
                    r'\.js_dialog[^{]*{[^}]*}',
                ]

                for pattern in wechat_css_patterns:
                    css_content = re.sub(pattern, '', css_content, flags=re.IGNORECASE | re.DOTALL)

                # 更新样式内容
                style.string = css_content.strip()

                # 如果样式为空，移除整个style标签
                if not style.string.strip():
                    style.decompose()

        # 移除可能包含JavaScript的属性
        unsafe_attrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onmouseout', 'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset']
        for tag in soup.find_all():
            for attr in unsafe_attrs:
                if tag.has_attr(attr):
                    del tag[attr]

        # 移除iframe标签（可能包含恶意内容）
        for iframe in soup.find_all('iframe'):
            iframe.decompose()

        # 移除object和embed标签
        for obj in soup.find_all(['object', 'embed']):
            obj.decompose()

        # 移除微信特有的元素和类
        self._remove_wechat_specific_elements(soup)

    def _remove_wechat_specific_elements(self, soup):
        """
        移除微信特有的元素，避免在浏览器中显示微信提示

        Args:
            soup: BeautifulSoup对象
        """
        try:
            # 移除微信特有的提示元素
            wechat_specific_selectors = [
                # 微信扫码提示相关
                '.weui-mask',
                '.weui-dialog',
                '.weui-toast',
                '.wx_dialog_container',
                '.wx_dialog',
                '.js_dialog',
                # 微信环境检测相关
                '.js_wx_tap_highlight',
                '.js_wx_tap_highlight_color',
                # 微信分享相关
                '.js_share_container',
                '.share_container',
                # 微信登录相关
                '.js_login_container',
                '.login_container',
                # 微信支付相关
                '.js_pay_container',
                '.pay_container',
                # 其他微信特有元素
                '.wx_loading',
                '.wx_tip',
                '.wx_warn',
                '.js_wx_loading'
            ]

            # 根据class选择器移除元素
            for selector in wechat_specific_selectors:
                class_name = selector[1:]  # 移除开头的点
                for element in soup.find_all(class_=class_name):
                    element.decompose()

            # 移除包含微信特定文本的元素
            wechat_texts = [
                '微信扫一扫',
                '使用完整服务',
                '请在微信客户端打开',
                '微信中打开',
                '长按识别二维码',
                '关注公众号'
            ]

            for text in wechat_texts:
                # 查找包含这些文本的元素并移除
                for element in soup.find_all(text=lambda t: t and text in t):
                    parent = element.parent
                    if parent and parent.name in ['div', 'span', 'p', 'section']:
                        # 检查父元素是否主要包含微信提示内容
                        parent_text = parent.get_text(strip=True)
                        if len(parent_text) < 100 and any(wx_text in parent_text for wx_text in wechat_texts):
                            parent.decompose()
                            break

            # 移除微信特有的meta标签
            wechat_meta_names = [
                'wx:card',
                'wx:timeline',
                'wx:appmsg',
                'wx:share'
            ]

            for meta_name in wechat_meta_names:
                for meta in soup.find_all('meta', {'name': meta_name}):
                    meta.decompose()
                for meta in soup.find_all('meta', {'property': meta_name}):
                    meta.decompose()

            # 移除微信JS SDK相关的元素
            for element in soup.find_all(id=lambda x: x and ('wx' in x.lower() or 'jssdk' in x.lower())):
                element.decompose()

        except Exception as e:
            logger.warning(f"移除微信特有元素失败: {str(e)}")

    def _fix_css_links(self, soup):
        """
        修复CSS样式文件链接，确保包含https协议

        Args:
            soup: BeautifulSoup对象
        """
        try:
            # 处理所有类型的link标签（不仅仅是stylesheet）
            links_to_remove = []
            for link in soup.find_all('link'):
                href = link.get('href')

                # 处理空href或无href的情况
                if not href or not href.strip():
                    # 对于某些类型的link，空href是无意义的，可以移除
                    rel_type = link.get('rel', [''])[0] if isinstance(link.get('rel'), list) else link.get('rel', '')
                    if rel_type in ['stylesheet', 'icon', 'shortcut icon', 'preload']:
                        links_to_remove.append(link)
                    continue

                # 跳过已经是完整URL的链接
                if href.startswith('http://') or href.startswith('https://'):
                    continue

                if href.startswith('//'):
                    # 协议相对路径，添加https
                    link['href'] = 'https:' + href
                elif href.startswith('/'):
                    # 绝对路径，添加完整域名
                    link['href'] = 'https://res.wx.qq.com' + href
                else:
                    # 相对路径，根据link类型决定基础域名
                    rel_type = link.get('rel', [''])[0] if isinstance(link.get('rel'), list) else link.get('rel', '')

                    if rel_type in ['stylesheet', 'preload']:
                        # CSS和预加载资源通常在res.wx.qq.com
                        link['href'] = 'https://res.wx.qq.com/mmbizwap/zh_CN/htmledition/' + href
                    elif rel_type in ['icon', 'shortcut icon']:
                        # 图标通常在主域名
                        link['href'] = 'https://mp.weixin.qq.com/' + href
                    else:
                        # 其他类型使用默认域名
                        link['href'] = 'https://res.wx.qq.com/' + href

            # 移除无效的link标签
            for link in links_to_remove:
                link.decompose()

            # 处理style标签中的@import和url()
            for style in soup.find_all('style'):
                if style.string:
                    css_content = style.string

                    # 修复@import中的URL
                    css_content = re.sub(r'@import\s+["\']?//([^"\';\s]+)["\']?', r'@import "https://\1"', css_content)
                    css_content = re.sub(r'@import\s+["\']?/([^"\';\s]+)["\']?', r'@import "https://res.wx.qq.com/\1"', css_content)
                    css_content = re.sub(r'@import\s+url\(["\']?//([^"\')\s]+)["\']?\)', r'@import url("https://\1")', css_content)
                    css_content = re.sub(r'@import\s+url\(["\']?/([^"\')\s]+)["\']?\)', r'@import url("https://res.wx.qq.com/\1")', css_content)

                    # 修复url()中的路径
                    css_content = re.sub(r'url\(["\']?//([^"\')\s]+)["\']?\)', r'url("https://\1")', css_content)
                    css_content = re.sub(r'url\(["\']?/([^"\')\s]+)["\']?\)', r'url("https://res.wx.qq.com/\1")', css_content)

                    # 修复相对路径的url()
                    css_content = re.sub(r'url\(["\']?\.\.?/([^"\')\s]+)["\']?\)', r'url("https://res.wx.qq.com/\1")', css_content)

                    style.string = css_content

        except Exception as e:
            logger.warning(f"CSS链接修复失败: {str(e)}")

    def _fix_image_tags(self, soup):
        """
        修复图片标签，确保src属性正确设置

        Args:
            soup: BeautifulSoup对象
        """
        try:
            for img in soup.find_all('img'):
                # 获取图片源链接，优先使用已有的src，然后是data-src
                src = img.get('src') or img.get('data-src')

                if src:
                    # 处理协议相对路径
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        # 微信图片通常在mmbiz.qpic.cn域名下
                        src = 'https://mmbiz.qpic.cn' + src

                    # 设置src属性
                    img['src'] = src

                    # 如果原来有data-src，也更新它
                    if img.get('data-src'):
                        # 处理data-src的路径
                        data_src = img.get('data-src')
                        if data_src.startswith('//'):
                            data_src = 'https:' + data_src
                        elif data_src.startswith('/'):
                            data_src = 'https://mmbiz.qpic.cn' + data_src
                        img['data-src'] = data_src

                # 确保图片有alt属性（有利于富文本编辑器）
                if not img.get('alt'):
                    img['alt'] = '图片'

        except Exception as e:
            logger.warning(f"图片标签修复失败: {str(e)}")

    def _restore_hidden_divs(self, soup):
        """
        恢复被隐藏的div标签，使其可见

        Args:
            soup: BeautifulSoup对象
        """
        try:
            for div in soup.find_all('div'):
                style = div.get('style', '')

                # 检查是否被隐藏
                if any(hidden_style in style.lower() for hidden_style in [
                    'display:none', 'display: none',
                    'visibility:hidden', 'visibility: hidden',
                    'opacity:0', 'opacity: 0'
                ]):
                    # 移除隐藏样式
                    style = re.sub(r'display\s*:\s*none\s*;?', '', style, flags=re.IGNORECASE)
                    style = re.sub(r'visibility\s*:\s*hidden\s*;?', '', style, flags=re.IGNORECASE)
                    style = re.sub(r'opacity\s*:\s*0\s*;?', '', style, flags=re.IGNORECASE)

                    # 清理多余的分号和空格
                    style = re.sub(r';\s*;', ';', style)
                    style = style.strip().rstrip(';')

                    if style:
                        div['style'] = style
                    else:
                        # 如果样式为空，移除style属性
                        if div.has_attr('style'):
                            del div['style']

        except Exception as e:
            logger.warning(f"恢复隐藏div失败: {str(e)}")

    def _compress_html(self, html_content: str) -> str:
        """
        压缩HTML内容，移除多余的换行和空白

        Args:
            html_content: 原始HTML内容

        Returns:
            str: 压缩后的HTML内容
        """
        try:
            # 移除多余的换行符
            html_content = re.sub(r'\r\n', '\n', html_content)
            html_content = re.sub(r'\r', '\n', html_content)

            # 移除多个连续的换行符，保留单个换行
            html_content = re.sub(r'\n\s*\n', '\n', html_content)

            # 移除行首行尾的空白字符
            lines = html_content.split('\n')
            lines = [line.strip() for line in lines if line.strip()]

            # 重新组合，使用单个换行符
            html_content = '\n'.join(lines)

            # 移除标签间的多余空白（但保留标签内的文本空白）
            html_content = re.sub(r'>\s+<', '><', html_content)

            return html_content

        except Exception as e:
            logger.warning(f"HTML压缩失败: {str(e)}")
            return html_content

    def _optimize_for_browser_display(self, soup):
        """
        优化HTML结构，确保在浏览器中正常显示文章内容

        Args:
            soup: BeautifulSoup对象
        """
        try:
            # 确保有基本的HTML结构
            if not soup.find('html'):
                # 如果没有html标签，创建基本结构
                html_tag = soup.new_tag('html')
                head_tag = soup.new_tag('head')
                body_tag = soup.new_tag('body')

                # 移动所有内容到body中
                for element in list(soup.children):
                    if element.name:
                        body_tag.append(element.extract())

                html_tag.append(head_tag)
                html_tag.append(body_tag)
                soup.append(html_tag)

            # 确保head标签存在并包含基本meta信息
            head = soup.find('head')
            if head:
                # 添加viewport meta标签，确保移动端正常显示
                if not head.find('meta', {'name': 'viewport'}):
                    viewport_meta = soup.new_tag('meta')
                    viewport_meta['name'] = 'viewport'
                    viewport_meta['content'] = 'width=device-width, initial-scale=1.0'
                    head.insert(0, viewport_meta)

                # 添加基本的CSS样式，确保内容可见
                basic_style = soup.new_tag('style')
                basic_style.string = """
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                        background: #fff;
                    }
                    img {
                        max-width: 100%;
                        height: auto;
                        display: block;
                        margin: 10px auto;
                    }
                    .rich_media_content {
                        font-size: 16px;
                        line-height: 1.8;
                    }
                    .rich_media_title {
                        font-size: 24px;
                        font-weight: bold;
                        margin: 20px 0;
                        text-align: center;
                    }
                    .rich_media_meta {
                        text-align: center;
                        color: #888;
                        margin: 10px 0 30px 0;
                    }
                    /* 隐藏可能的遮罩层 */
                    .weui-mask, .wx_dialog_container, .js_dialog {
                        display: none !important;
                    }
                """
                head.append(basic_style)

            # 移除可能导致页面不可见的CSS样式
            for element in soup.find_all():
                if element.get('style'):
                    style = element['style']
                    # 移除可能隐藏整个页面的样式
                    problematic_styles = [
                        'position: fixed',
                        'position: absolute',
                        'z-index: 9999',
                        'z-index: 999',
                        'top: 0',
                        'left: 0',
                        'right: 0',
                        'bottom: 0'
                    ]

                    for prob_style in problematic_styles:
                        if prob_style in style.lower():
                            # 如果元素包含这些样式且可能是遮罩层，移除它
                            element_text = element.get_text(strip=True)
                            if len(element_text) < 50 or any(wx_text in element_text for wx_text in ['微信', '扫码', '客户端']):
                                element.decompose()
                                break

            # 确保主要内容区域可见
            main_content = soup.find('div', class_='rich_media_content')
            if main_content:
                # 移除可能隐藏内容的样式
                if main_content.get('style'):
                    style = main_content['style']
                    style = re.sub(r'display\s*:\s*none\s*;?', '', style, flags=re.IGNORECASE)
                    style = re.sub(r'visibility\s*:\s*hidden\s*;?', '', style, flags=re.IGNORECASE)
                    main_content['style'] = style

        except Exception as e:
            logger.warning(f"浏览器显示优化失败: {str(e)}")
    
    def _process_images(self, content_elem):
        """处理内容中的图片链接，优化富文本编辑器显示"""
        try:
            images = content_elem.find_all('img')
            for img in images:
                # 获取图片源链接
                src = img.get('data-src') or img.get('src')
                if src:
                    # 确保图片链接是完整的URL
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = 'https://mmbiz.qpic.cn' + src
                    img['src'] = src

                # 处理图片尺寸信息
                self._process_image_dimensions(img)

                # 处理图片样式
                self._process_image_styles(img)

                # 保留有用的属性，移除微信特有的属性
                useful_attrs = ['src', 'alt', 'title', 'width', 'height', 'style', 'class']
                attrs_to_remove = []
                for attr in list(img.attrs.keys()):
                    if attr not in useful_attrs and not attr.startswith('data-'):
                        attrs_to_remove.append(attr)

                for attr in attrs_to_remove:
                    del img[attr]

        except Exception as e:
            logger.warning(f"图片处理失败: {str(e)}")

    def _process_image_dimensions(self, img):
        """处理图片尺寸信息"""
        try:
            # 从data-w和data-ratio计算合适的显示尺寸
            data_w = img.get('data-w')
            data_ratio = img.get('data-ratio')

            if data_w and data_ratio:
                try:
                    width = int(data_w)
                    ratio = float(data_ratio)
                    height = int(width * ratio)

                    # 限制最大宽度，适应富文本编辑器
                    max_width = 600
                    if width > max_width:
                        height = int(height * max_width / width)
                        width = max_width

                    img['width'] = str(width)
                    img['height'] = str(height)
                except (ValueError, ZeroDivisionError):
                    pass

            # 如果没有尺寸信息，设置默认最大宽度
            if not img.get('width') and not img.get('style'):
                img['style'] = 'max-width: 100%; height: auto;'

        except Exception as e:
            logger.warning(f"图片尺寸处理失败: {str(e)}")

    def _process_image_styles(self, img):
        """处理图片样式，优化wangEditor显示效果"""
        try:
            current_style = img.get('style', '')

            # 解析现有样式
            existing_styles = {}
            if current_style:
                for rule in current_style.split(';'):
                    if ':' in rule:
                        prop, value = rule.split(':', 1)
                        existing_styles[prop.strip().lower()] = value.strip()

            # wangEditor图片样式优化
            wangeditor_image_styles = {
                'max-width': '100%',
                'height': 'auto',
                'display': 'block',
                'margin': '10px auto',  # 居中显示
                'border-radius': '4px'  # 圆角效果
            }

            # 保留原有的重要样式，添加wangEditor优化样式
            for prop, value in wangeditor_image_styles.items():
                if prop not in existing_styles:
                    existing_styles[prop] = value

            # 特殊处理：如果有宽度设置，确保不超过容器
            if 'width' in existing_styles:
                width_value = existing_styles['width']
                # 如果宽度是像素值且大于600，限制为600px
                if width_value.endswith('px'):
                    try:
                        width_num = int(width_value[:-2])
                        if width_num > 600:
                            existing_styles['width'] = '600px'
                    except ValueError:
                        pass

            # 重新构建样式字符串
            style_parts = [f"{prop}: {value}" for prop, value in existing_styles.items()]
            img['style'] = '; '.join(style_parts)

        except Exception as e:
            logger.warning(f"图片样式处理失败: {str(e)}")
    
    def _clean_content(self, content_elem):
        """清理内容中不必要的标签和属性，保留wangEditor需要的样式"""
        try:
            # 移除脚本标签，保留样式标签
            for tag in content_elem.find_all(['script']):
                tag.decompose()

            # 保留样式标签，wangEditor需要CSS样式
            for style_tag in content_elem.find_all('style'):
                style_content = style_tag.string
                if style_content:
                    # 提取并优化CSS样式
                    useful_styles = self._extract_wangeditor_styles(style_content)
                    if useful_styles:
                        style_tag.string = useful_styles
                    else:
                        # 即使没有提取到有用样式，也保留原始样式标签
                        pass

            # 保留更多属性，确保样式不丢失
            for tag in content_elem.find_all():
                # wangEditor支持的属性列表（更宽松的策略）
                allowed_attrs = [
                    'src', 'href', 'alt', 'title', 'width', 'height',
                    'style', 'class', 'id', 'align', 'valign',
                    'border', 'cellpadding', 'cellspacing',
                    'colspan', 'rowspan', 'target', 'rel',
                    'color', 'face', 'size'  # 字体相关属性
                ]

                attrs_to_remove = []
                for attr in list(tag.attrs.keys()):
                    # 保留data-*属性
                    if attr.startswith('data-'):
                        continue
                    # 保留微信样式相关属性
                    if attr.startswith('wx'):
                        continue
                    # 保留允许的属性
                    if attr not in allowed_attrs:
                        # 只移除明确无用的属性
                        if attr in ['onclick', 'onload', 'onerror', 'contenteditable']:
                            attrs_to_remove.append(attr)

                for attr in attrs_to_remove:
                    del tag[attr]

                # 增强style属性处理，保留更多样式
                if tag.has_attr('style'):
                    enhanced_style = self._enhance_inline_style_for_wangeditor(tag['style'])
                    if enhanced_style:
                        tag['style'] = enhanced_style

        except Exception as e:
            logger.warning(f"内容清理失败: {str(e)}")

    def _extract_wangeditor_styles(self, style_content: str) -> str:
        """提取适合wangEditor的CSS样式"""
        try:
            useful_rules = []
            # wangEditor支持的样式规则（更全面的保留策略）
            useful_patterns = [
                r'\.rich_media_content[^{]*{[^}]*}',  # 主要内容样式
                r'p[^{]*{[^}]*}',  # 段落样式
                r'h[1-6][^{]*{[^}]*}',  # 标题样式
                r'img[^{]*{[^}]*}',  # 图片样式
                r'blockquote[^{]*{[^}]*}',  # 引用样式
                r'table[^{]*{[^}]*}',  # 表格样式
                r'td[^{]*{[^}]*}',  # 表格单元格样式
                r'th[^{]*{[^}]*}',  # 表格标题样式
                r'ul[^{]*{[^}]*}',  # 无序列表样式
                r'ol[^{]*{[^}]*}',  # 有序列表样式
                r'li[^{]*{[^}]*}',  # 列表项样式
                r'a[^{]*{[^}]*}',  # 链接样式
                r'strong[^{]*{[^}]*}',  # 粗体样式
                r'em[^{]*{[^}]*}',  # 斜体样式
                r'span[^{]*{[^}]*}',  # span样式
                r'div[^{]*{[^}]*}',  # div样式
                r'section[^{]*{[^}]*}',  # section样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*color[^}]*}',  # 颜色相关样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*font[^}]*}',  # 字体相关样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*text-align[^}]*}',  # 对齐样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*background[^}]*}',  # 背景样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*border[^}]*}',  # 边框样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*margin[^}]*}',  # 外边距样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*padding[^}]*}',  # 内边距样式
                r'\.[a-zA-Z_-]+[^{]*{[^}]*line-height[^}]*}',  # 行高样式
            ]

            import re
            for pattern in useful_patterns:
                matches = re.findall(pattern, style_content, re.IGNORECASE | re.DOTALL)
                useful_rules.extend(matches)

            # 去重并返回
            unique_rules = list(dict.fromkeys(useful_rules))
            return '\n'.join(unique_rules) if unique_rules else style_content
        except Exception as e:
            logger.warning(f"wangEditor样式提取失败: {str(e)}")
            return style_content  # 失败时返回原始样式

    def _enhance_inline_style_for_wangeditor(self, style_attr: str) -> str:
        """增强内联样式，确保wangEditor能正确显示"""
        try:
            # wangEditor支持的样式属性（更全面的保留）
            wangeditor_style_props = [
                'color', 'background-color', 'background', 'font-size', 'font-weight',
                'font-family', 'font-style', 'text-align', 'text-decoration',
                'line-height', 'margin', 'margin-top', 'margin-bottom', 'margin-left', 'margin-right',
                'padding', 'padding-top', 'padding-bottom', 'padding-left', 'padding-right',
                'width', 'height', 'max-width', 'max-height', 'min-width', 'min-height',
                'border', 'border-top', 'border-bottom', 'border-left', 'border-right',
                'border-radius', 'border-color', 'border-width', 'border-style',
                'display', 'float', 'clear', 'vertical-align', 'text-indent',
                'letter-spacing', 'word-spacing', 'white-space', 'overflow',
                'position', 'top', 'bottom', 'left', 'right', 'z-index'
            ]

            # 解析并保留样式属性
            style_rules = []
            for rule in style_attr.split(';'):
                if ':' in rule:
                    prop, value = rule.split(':', 1)
                    prop = prop.strip().lower()
                    value = value.strip()

                    # 保留wangEditor支持的样式属性
                    if any(useful_prop == prop or prop.startswith(useful_prop) for useful_prop in wangeditor_style_props):
                        # 特殊处理一些样式值
                        if prop == 'font-size' and value:
                            # 确保字体大小有单位
                            if value.isdigit():
                                value = value + 'px'
                        elif prop == 'color' and value:
                            # 确保颜色值格式正确
                            if value.startswith('rgb') or value.startswith('#') or value in ['red', 'blue', 'green', 'black', 'white']:
                                pass  # 保持原样

                        style_rules.append(f"{prop}: {value}")

            return '; '.join(style_rules) if style_rules else style_attr
        except Exception as e:
            logger.warning(f"wangEditor样式增强失败: {str(e)}")
            return style_attr  # 失败时返回原始样式

    def _preprocess_content_structure(self, content_elem):
        """预处理内容结构，优化富文本编辑器显示"""
        try:
            # 处理微信特有的段落结构
            for p in content_elem.find_all('p'):
                # 移除空的段落
                if not p.get_text(strip=True) and not p.find('img'):
                    p.decompose()
                    continue

                # 处理段落中的换行
                for br in p.find_all('br'):
                    # 将连续的br标签转换为段落分隔
                    if br.next_sibling and br.next_sibling.name == 'br':
                        br.decompose()

            # 处理微信的section结构
            for section in content_elem.find_all('section'):
                # 如果section只是简单的容器，将其内容提升到父级
                if not section.get('style') and not section.get('class'):
                    section.unwrap()

        except Exception as e:
            logger.warning(f"内容结构预处理失败: {str(e)}")

    def _process_text_formatting(self, content_elem):
        """处理文本格式，确保富文本编辑器兼容"""
        try:
            # 处理强调标签
            for tag in content_elem.find_all(['strong', 'b']):
                if tag.name == 'b':
                    tag.name = 'strong'

            # 处理斜体标签
            for tag in content_elem.find_all(['em', 'i']):
                if tag.name == 'i':
                    tag.name = 'em'

            # 处理下划线
            for tag in content_elem.find_all('u'):
                # 保持u标签，大多数富文本编辑器支持
                pass

            # 处理删除线
            for tag in content_elem.find_all(['s', 'strike', 'del']):
                tag.name = 'del'  # 统一使用del标签

            # 处理标题标签
            for i in range(1, 7):
                for h in content_elem.find_all(f'h{i}'):
                    # 确保标题有适当的样式
                    current_style = h.get('style', '')
                    if 'font-weight' not in current_style:
                        h['style'] = current_style + '; font-weight: bold;' if current_style else 'font-weight: bold;'

        except Exception as e:
            logger.warning(f"文本格式处理失败: {str(e)}")

    def _process_tables_and_lists(self, content_elem):
        """处理表格和列表，确保富文本编辑器兼容"""
        try:
            # 处理表格
            for table in content_elem.find_all('table'):
                # 确保表格有基本样式
                current_style = table.get('style', '')
                if 'border-collapse' not in current_style:
                    table['style'] = current_style + '; border-collapse: collapse; width: 100%;' if current_style else 'border-collapse: collapse; width: 100%;'

                # 处理表格单元格
                for td in table.find_all(['td', 'th']):
                    cell_style = td.get('style', '')
                    if 'border' not in cell_style:
                        td['style'] = cell_style + '; border: 1px solid #ddd; padding: 8px;' if cell_style else 'border: 1px solid #ddd; padding: 8px;'

            # 处理列表
            for ul in content_elem.find_all('ul'):
                # 确保列表有适当的样式
                current_style = ul.get('style', '')
                if 'list-style-type' not in current_style:
                    ul['style'] = current_style + '; list-style-type: disc; padding-left: 20px;' if current_style else 'list-style-type: disc; padding-left: 20px;'

            for ol in content_elem.find_all('ol'):
                current_style = ol.get('style', '')
                if 'list-style-type' not in current_style:
                    ol['style'] = current_style + '; list-style-type: decimal; padding-left: 20px;' if current_style else 'list-style-type: decimal; padding-left: 20px;'

        except Exception as e:
            logger.warning(f"表格和列表处理失败: {str(e)}")

    def _postprocess_for_wangeditor(self, content_elem):
        """后处理，确保wangEditor兼容性和样式效果"""
        try:
            # 确保所有图片都有alt属性和合适的样式
            for img in content_elem.find_all('img'):
                if not img.get('alt'):
                    img['alt'] = '图片'

                # 确保图片有合适的样式类
                img_class = img.get('class', [])
                if isinstance(img_class, str):
                    img_class = [img_class]
                if 'wangeditor-img' not in img_class:
                    img_class.append('wangeditor-img')
                img['class'] = img_class

            # 处理链接
            for a in content_elem.find_all('a'):
                href = a.get('href', '')
                if href.startswith('http') and 'target' not in a.attrs:
                    a['target'] = '_blank'
                    a['rel'] = 'noopener noreferrer'

            # 处理段落，确保有合适的样式
            for p in content_elem.find_all('p'):
                current_style = p.get('style', '')
                if not current_style:
                    # 为没有样式的段落添加基础样式
                    p['style'] = 'margin: 10px 0; line-height: 1.6;'
                elif 'line-height' not in current_style:
                    # 为有样式但没有行高的段落添加行高
                    p['style'] = current_style + '; line-height: 1.6;'

            # 处理标题，确保有合适的样式
            for i in range(1, 7):
                for h in content_elem.find_all(f'h{i}'):
                    current_style = h.get('style', '')
                    if 'font-weight' not in current_style:
                        h['style'] = current_style + '; font-weight: bold;' if current_style else 'font-weight: bold;'

            # 不移除空标签，保留原始结构
            # wangEditor可能需要这些结构来维持样式

            # 确保根容器是div
            if content_elem.name != 'div':
                content_elem.name = 'div'

            # 添加wangEditor友好的class和样式
            current_class = content_elem.get('class', [])
            if isinstance(current_class, str):
                current_class = [current_class]

            # 添加多个有用的class
            useful_classes = ['wangeditor-content', 'rich-text-content', 'rich_media_content']
            for cls in useful_classes:
                if cls not in current_class:
                    current_class.append(cls)
            content_elem['class'] = current_class

            # 为根容器添加基础样式
            root_style = content_elem.get('style', '')
            if not root_style:
                content_elem['style'] = 'font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; font-size: 16px; line-height: 1.6; color: #333;'

        except Exception as e:
            logger.warning(f"wangEditor兼容性处理失败: {str(e)}")
    
    def _extract_cover_image(self, soup) -> str:
        """提取文章封面图"""
        try:
            # 尝试从meta标签中获取
            meta_image = soup.find('meta', {'property': 'og:image'})
            if meta_image and meta_image.get('content'):
                return meta_image['content']
            
            # 尝试从文章内容中的第一张图片获取
            first_img = soup.find('img')
            if first_img:
                src = first_img.get('data-src') or first_img.get('src')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    return src
            
            return ""
        except Exception as e:
            logger.warning(f"封面图提取失败: {str(e)}")
            return ""
    
    def crawl_article(self, url: str) -> Dict:
        """
        爬取微信公众号文章
        
        Args:
            url: 微信公众号文章URL
            
        Returns:
            Dict: 爬取的文章信息
            
        Raises:
            Exception: 爬取失败时抛出异常
        """
        try:
            # 验证URL
            if not self.is_wechat_url(url):
                raise Exception("无效的微信公众号文章URL")
            
            logger.info(f"开始爬取文章: {url}")
            
            # 发送请求
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # 检查是否被重定向或blocked
            if '请在微信客户端打开链接' in response.text:
                raise Exception("文章需要在微信客户端中打开，无法直接爬取")
            
            if response.status_code != 200:
                raise Exception(f"请求失败，状态码: {response.status_code}")
            
            # 提取文章内容
            article_data = self.extract_article_content(response.text)
            article_data['original_url'] = url
            
            logger.info(f"文章爬取成功: {article_data['title']}")
            return article_data
            
        except requests.exceptions.Timeout:
            raise Exception("请求超时，请检查网络连接")
        except requests.exceptions.RequestException as e:
            raise Exception(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"文章爬取失败: {str(e)}")
            raise

    def _extract_css_rules(self, soup: BeautifulSoup) -> Dict[str, Dict[str, str]]:
        """提取CSS规则"""
        try:
            css_rules = {}

            # 提取所有style标签中的CSS
            for style_tag in soup.find_all('style'):
                if style_tag.string:
                    css_content = style_tag.string
                    rules = self._parse_css_content(css_content)
                    css_rules.update(rules)

            return css_rules
        except Exception as e:
            logger.warning(f"CSS规则提取失败: {str(e)}")
            return {}

    def _parse_css_content(self, css_content: str) -> Dict[str, Dict[str, str]]:
        """解析CSS内容"""
        try:
            rules = {}

            # 简单的CSS解析，匹配选择器和样式块
            css_pattern = r'([^{]+)\s*\{\s*([^}]+)\s*\}'
            matches = re.findall(css_pattern, css_content, re.DOTALL)

            for selector, styles in matches:
                selector = selector.strip()

                # 解析样式属性
                style_dict = {}
                style_declarations = styles.split(';')

                for declaration in style_declarations:
                    if ':' in declaration:
                        prop, value = declaration.split(':', 1)
                        prop = prop.strip()
                        value = value.strip()
                        if prop and value:
                            style_dict[prop] = value

                if style_dict:
                    rules[selector] = style_dict

            return rules
        except Exception as e:
            logger.warning(f"CSS内容解析失败: {str(e)}")
            return {}

    def _apply_css_rules_to_elements(self, soup: BeautifulSoup, css_rules: Dict[str, Dict[str, str]]):
        """将CSS规则应用到HTML元素"""
        try:
            for selector, styles in css_rules.items():
                # 处理不同类型的选择器
                elements = self._find_elements_by_selector(soup, selector)

                for element in elements:
                    self._apply_styles_to_element(element, styles)

        except Exception as e:
            logger.warning(f"CSS规则应用失败: {str(e)}")

    def _find_elements_by_selector(self, soup: BeautifulSoup, selector: str) -> List:
        """根据CSS选择器查找元素（增强版）"""
        try:
            elements = []
            selector = selector.strip()

            # 处理类选择器 .class
            if selector.startswith('.') and not ' ' in selector:
                class_name = selector[1:]
                elements = soup.find_all(class_=lambda x: x and class_name in x if isinstance(x, list) else class_name == x if x else False)

            # 处理ID选择器 #id
            elif selector.startswith('#') and not ' ' in selector:
                id_name = selector[1:]
                element = soup.find(id=id_name)
                if element:
                    elements = [element]

            # 处理标签选择器 tag
            elif selector.replace('-', '').replace('_', '').isalpha() and not ' ' in selector:
                elements = soup.find_all(selector)

            # 处理复合选择器
            elif ' ' in selector:
                elements = self._handle_complex_selector(soup, selector)

            # 处理标签.类名选择器 如 p.class
            elif '.' in selector and not selector.startswith('.'):
                tag, class_name = selector.split('.', 1)
                elements = soup.find_all(tag, class_=lambda x: x and class_name in x if isinstance(x, list) else class_name == x if x else False)

            # 处理通用选择器和其他情况
            else:
                # 尝试使用BeautifulSoup的CSS选择器（如果支持）
                try:
                    elements = soup.select(selector)
                except:
                    # 如果CSS选择器不支持，尝试简单匹配
                    if selector == '*':
                        elements = soup.find_all()
                    else:
                        elements = soup.find_all(selector)

            return elements
        except Exception as e:
            logger.warning(f"选择器查找失败 {selector}: {str(e)}")
            return []

    def _handle_complex_selector(self, soup: BeautifulSoup, selector: str) -> List:
        """处理复杂选择器"""
        try:
            elements = []

            # 处理后代选择器 如 .parent .child
            if ' ' in selector:
                parts = selector.split()
                current_elements = [soup]

                for part in parts:
                    next_elements = []
                    for current in current_elements:
                        found = self._find_elements_by_selector(current, part)
                        next_elements.extend(found)
                    current_elements = next_elements

                elements = current_elements

            return elements
        except Exception as e:
            logger.warning(f"复杂选择器处理失败 {selector}: {str(e)}")
            return []

    def _apply_styles_to_element(self, element, styles: Dict[str, str]):
        """将样式应用到元素（增强版）"""
        try:
            current_style = element.get('style', '')

            # 解析现有的内联样式
            existing_styles = self._parse_inline_style(current_style)

            # 合并新样式，保持重要样式的优先级
            for prop, value in styles.items():
                # 如果现有样式中没有这个属性，或者新值更重要，则应用新样式
                if prop not in existing_styles:
                    existing_styles[prop] = value
                elif self._is_more_important_style(prop, value, existing_styles[prop]):
                    existing_styles[prop] = value

            # 重新构建style属性
            if existing_styles:
                element['style'] = self._build_style_string(existing_styles)

        except Exception as e:
            logger.warning(f"样式应用失败: {str(e)}")

    def _is_more_important_style(self, prop: str, new_value: str, existing_value: str) -> bool:
        """判断新样式是否更重要"""
        try:
            # 对于某些关键属性，优先使用更具体的值
            if prop == 'color' and new_value.startswith('rgb') and not existing_value.startswith('rgb'):
                return True
            elif prop == 'font-size' and 'px' in new_value and 'px' not in existing_value:
                return True
            elif prop == 'margin' and new_value != '0' and existing_value == '0':
                return True
            elif prop == 'padding' and new_value != '0' and existing_value == '0':
                return True

            return False
        except Exception:
            return False

    def _inline_all_styles(self, content_elem):
        """将所有CSS样式内联化，确保富文本编辑器正确显示"""
        try:
            logger.info("开始CSS内联化处理...")

            # 1. 提取CSS规则
            css_rules = self._extract_css_rules(content_elem)
            logger.info(f"提取到 {len(css_rules)} 条CSS规则")

            # 2. 应用CSS规则到对应元素
            self._apply_css_rules_to_elements(content_elem, css_rules)

            # 3. 移除style标签（已经内联化）
            for style_tag in content_elem.find_all('style'):
                style_tag.decompose()

            # 4. 强化关键元素的样式
            self._enhance_critical_element_styles(content_elem)

            # 5. 确保所有元素都有基础样式
            self._ensure_basic_styles(content_elem)

            logger.info("CSS内联化处理完成")

        except Exception as e:
            logger.error(f"CSS内联化失败: {str(e)}")

    def _enhance_critical_element_styles(self, content_elem):
        """强化关键元素的样式"""
        try:
            # 强化段落样式
            for p in content_elem.find_all('p'):
                current_style = p.get('style', '')
                essential_styles = {
                    'margin': '10px 0',
                    'line-height': '1.6',
                    'font-size': '16px'
                }

                # 解析现有样式
                existing_styles = self._parse_inline_style(current_style)

                # 添加缺失的基础样式
                for prop, value in essential_styles.items():
                    if prop not in existing_styles:
                        existing_styles[prop] = value

                # 重新设置样式
                p['style'] = self._build_style_string(existing_styles)

            # 强化图片样式
            for img in content_elem.find_all('img'):
                current_style = img.get('style', '')
                essential_styles = {
                    'max-width': '100%',
                    'height': 'auto',
                    'display': 'block',
                    'margin': '10px auto'
                }

                existing_styles = self._parse_inline_style(current_style)
                for prop, value in essential_styles.items():
                    if prop not in existing_styles:
                        existing_styles[prop] = value

                img['style'] = self._build_style_string(existing_styles)

            # 强化标题样式
            for i in range(1, 7):
                for h in content_elem.find_all(f'h{i}'):
                    current_style = h.get('style', '')
                    existing_styles = self._parse_inline_style(current_style)

                    if 'font-weight' not in existing_styles:
                        existing_styles['font-weight'] = 'bold'
                    if 'margin' not in existing_styles:
                        existing_styles['margin'] = '20px 0 10px 0'

                    h['style'] = self._build_style_string(existing_styles)

        except Exception as e:
            logger.warning(f"关键元素样式强化失败: {str(e)}")

    def _ensure_basic_styles(self, content_elem):
        """确保所有元素都有基础样式"""
        try:
            # 为根容器添加基础样式
            if content_elem.name == 'div':
                current_style = content_elem.get('style', '')
                existing_styles = self._parse_inline_style(current_style)

                basic_styles = {
                    'font-family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    'font-size': '16px',
                    'line-height': '1.6',
                    'color': '#333',
                    'word-wrap': 'break-word'
                }

                for prop, value in basic_styles.items():
                    if prop not in existing_styles:
                        existing_styles[prop] = value

                content_elem['style'] = self._build_style_string(existing_styles)

        except Exception as e:
            logger.warning(f"基础样式设置失败: {str(e)}")

    def _parse_inline_style(self, style_string: str) -> Dict[str, str]:
        """解析内联样式字符串"""
        styles = {}
        if style_string:
            for declaration in style_string.split(';'):
                if ':' in declaration:
                    prop, value = declaration.split(':', 1)
                    styles[prop.strip()] = value.strip()
        return styles

    def _build_style_string(self, styles: Dict[str, str]) -> str:
        """构建样式字符串"""
        return '; '.join([f"{prop}: {value}" for prop, value in styles.items() if value])

# 创建全局实例
wechat_crawler = WeChatCrawler()
